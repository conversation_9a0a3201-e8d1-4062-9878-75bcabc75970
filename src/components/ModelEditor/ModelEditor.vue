<template>
  <div class="model-editor">
    <!-- 头部 -->
    <div class="header" :class="{ 'hidden': isFullscreen }">
      <h1 class="text-xl font-bold text-white">Three.js GLTF 模型查看器</h1>
    </div>

    <!-- 控制面板 -->
    <div class="controls" :class="{ 'hidden': isFullscreen }">
      <input
        ref="fileInput"
        type="file"
        accept=".gltf,.glb"
        class="hidden"
        @change="handleFileSelect"
      />
      <button
        @click="selectFile"
        class="btn-primary"
      >
        选择 GLTF/GLB 文件
      </button>

      <button
        v-if="showExportButton"
        @click="exportModel"
        class="btn-export"
        :disabled="isExporting"
      >
        {{ isExporting ? '导出中...' : '导出为 GLB' }}
      </button>

      <button
        @click="toggleFullscreen"
        class="btn-fullscreen"
        :title="isFullscreen ? '退出全屏' : '全屏查看'"
      >
        {{ isFullscreen ? '退出全屏' : '全屏查看' }}
      </button>

      <div class="model-info">
        {{ modelInfo }}
      </div>
    </div>

    <!-- 场景容器 -->
    <div class="scene-container" :class="{ 'fullscreen': isFullscreen }" ref="sceneContainer">
      <!-- 动画面板 -->
      <div v-if="showAnimationsPanel" class="animations-panel">
        <h3 class="text-lg font-semibold mb-4">动作列表</h3>
        <div v-if="animations.length === 0" class="no-animations">
          没有可用的动作
        </div>
        <div v-else class="animations-list">
          <div
            v-for="(animation, index) in animations"
            :key="index"
            class="animation-item"
            :class="{ active: index === activeAnimationIndex }"
          >
            <!-- 动画名称 -->
            <div
              v-if="editingIndex !== index"
              class="animation-name"
              @click="startEditing(index)"
              :title="'点击编辑动作名称'"
            >
              {{ animation.name }}
            </div>

            <!-- 编辑输入框 -->
            <input
              v-else
              ref="editInput"
              v-model="editingName"
              class="animation-name-edit"
              @keydown.enter="finishEditing(true)"
              @keydown.escape="finishEditing(false)"
              @blur="finishEditing(true)"
            />

            <!-- 控制按钮 -->
            <div class="animation-controls">
              <button
                @click="playAnimation(index)"
                class="btn btn-play"
                :disabled="index === activeAnimationIndex"
              >
                {{ index === activeAnimationIndex ? '正在播放' : '播放' }}
              </button>

              <button
                @click="stopAnimation"
                class="btn btn-stop"
                :disabled="index !== activeAnimationIndex"
              >
                停止
              </button>

              <button
                @click="deleteAnimation(index)"
                class="btn btn-delete"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载提示 -->
      <div v-if="isLoading" class="loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">{{ loadingText }}</div>
        </div>
      </div>

      <!-- 操作说明 -->
      <div class="instructions" :class="{ 'hidden': isFullscreen }">
        <p class="font-semibold mb-1">操作说明：</p>
        <p>- 左键拖动：旋转模型</p>
        <p>- 右键拖动：平移模型</p>
        <p>- 滚轮：缩放模型</p>
      </div>

      <!-- 全屏模式下的浮动控制按钮 -->
      <div v-if="isFullscreen" class="fullscreen-controls">
        <button
          @click="toggleFullscreen"
          class="btn-exit-fullscreen"
          title="退出全屏 (ESC)"
        >
          ×
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ModelEditorService, type ModelInfo, type AnimationInfo } from '../../services/modelEditorService'

// 响应式数据
const sceneContainer = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()
const editInput = ref<HTMLInputElement[]>()

const isLoading = ref(false)
const loadingText = ref('正在加载模型，请稍候...')
const isExporting = ref(false)
const modelInfo = ref('未加载模型')
const showExportButton = ref(false)
const showAnimationsPanel = ref(false)
const isFullscreen = ref(false)

const animations = ref<AnimationInfo[]>([])
const activeAnimationIndex = ref(-1)

// 编辑状态
const editingIndex = ref(-1)
const editingName = ref('')

// 模型编辑器服务实例
let modelEditor: ModelEditorService | null = null

// 初始化
onMounted(async () => {
  if (sceneContainer.value) {
    try {
      isLoading.value = true
      loadingText.value = '正在初始化模型编辑器...'

      modelEditor = new ModelEditorService()
      await modelEditor.init(sceneContainer.value)

      // 添加键盘事件监听
      window.addEventListener('keydown', handleKeydown)

      console.log('模型编辑器初始化成功')
    } catch (error) {
      console.error('初始化模型编辑器失败:', error)
      ElMessage.error('初始化模型编辑器失败')
    } finally {
      isLoading.value = false
    }
  }
})

// 清理资源
onUnmounted(() => {
  if (modelEditor) {
    modelEditor.dispose()
    modelEditor = null
  }
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeydown)
})

// 选择文件
const selectFile = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  if (!file.name.toLowerCase().endsWith('.gltf') && !file.name.toLowerCase().endsWith('.glb')) {
    ElMessage.error('请选择 .gltf 或 .glb 格式的文件')
    return
  }

  if (!modelEditor) {
    ElMessage.error('模型编辑器未初始化')
    return
  }

  try {
    isLoading.value = true
    loadingText.value = `正在加载: ${file.name}`

    const info: ModelInfo = await modelEditor.loadGLTFModel(file)

    // 更新模型信息
    modelInfo.value = `${info.name} | 顶点数: ${info.vertices} | 大小: ${ModelEditorService.formatFileSize(info.size)}`

    // 更新动画列表
    updateAnimationsList()

    // 重置导出按钮状态
    showExportButton.value = false

    ElMessage.success('模型加载成功')
  } catch (error) {
    console.error('加载模型失败:', error)
    ElMessage.error('加载模型失败: ' + (error as Error).message)
    modelInfo.value = '加载失败: ' + file.name
  } finally {
    isLoading.value = false
    // 清空文件输入，允许重新选择同一文件
    target.value = ''
  }
}

// 更新动画列表
const updateAnimationsList = () => {
  if (!modelEditor) return

  animations.value = modelEditor.getAnimations()
  activeAnimationIndex.value = modelEditor.getActiveAnimationIndex()
  showAnimationsPanel.value = animations.value.length > 0
}

// 播放动画
const playAnimation = (index: number) => {
  if (!modelEditor) return

  const success = modelEditor.playAnimation(index)
  if (success) {
    activeAnimationIndex.value = index
    ElMessage.success(`开始播放: ${animations.value[index].name}`)
  } else {
    ElMessage.error('播放动画失败')
  }
}

// 停止动画
const stopAnimation = () => {
  if (!modelEditor) return

  const currentIndex = activeAnimationIndex.value
  modelEditor.stopAnimation()
  activeAnimationIndex.value = -1

  if (currentIndex >= 0 && currentIndex < animations.value.length) {
    ElMessage.info(`停止播放: ${animations.value[currentIndex].name}`)
  }
}

// 删除动画
const deleteAnimation = (index: number) => {
  if (!modelEditor) return

  const animationName = animations.value[index].name
  const success = modelEditor.deleteAnimation(index)

  if (success) {
    updateAnimationsList()

    // 如果模型被编辑过，显示导出按钮
    if (modelEditor.isModelChanged()) {
      showExportButton.value = true
    }

    // 如果没有动画了，隐藏面板
    if (animations.value.length === 0) {
      showAnimationsPanel.value = false
    }

    ElMessage.success(`删除动画: ${animationName}`)
  } else {
    ElMessage.error('删除动画失败')
  }
}

// 开始编辑动画名称
const startEditing = (index: number) => {
  editingIndex.value = index
  editingName.value = animations.value[index].name

  nextTick(() => {
    const inputs = editInput.value
    if (inputs && inputs[0]) {
      inputs[0].focus()
      inputs[0].select()
    }
  })
}

// 完成编辑动画名称
const finishEditing = (save: boolean) => {
  if (editingIndex.value === -1) return

  const index = editingIndex.value
  const newName = editingName.value.trim()
  const originalName = animations.value[index].name

  if (save && newName && newName !== originalName && modelEditor) {
    const success = modelEditor.renameAnimation(index, newName)

    if (success) {
      animations.value[index].name = newName

      // 如果模型被编辑过，显示导出按钮
      if (modelEditor.isModelChanged()) {
        showExportButton.value = true
      }

      ElMessage.success(`动画名称已更改为: ${newName}`)
    } else {
      ElMessage.error('重命名动画失败')
    }
  }

  // 重置编辑状态
  editingIndex.value = -1
  editingName.value = ''
}

// 导出模型
const exportModel = async () => {
  if (!modelEditor) {
    ElMessage.error('没有可导出的模型')
    return
  }

  try {
    isExporting.value = true
    loadingText.value = '正在导出模型...'
    isLoading.value = true

    const buffer = await modelEditor.exportToGLB()
    const filename = modelEditor.getExportFileName()

    // 创建下载链接
    const blob = new Blob([buffer], { type: 'application/octet-stream' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()

    // 清理
    setTimeout(() => {
      URL.revokeObjectURL(url)
    }, 100)

    ElMessage.success('模型导出成功')
  } catch (error) {
    console.error('导出模型失败:', error)
    ElMessage.error('导出模型失败: ' + (error as Error).message)
  } finally {
    isExporting.value = false
    isLoading.value = false
  }
}

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value

  // 延迟调用窗口大小变化处理，确保 DOM 更新完成
  nextTick(() => {
    if (modelEditor) {
      // 触发窗口大小变化事件，让 Three.js 重新计算渲染器大小
      window.dispatchEvent(new Event('resize'))
    }
  })
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  // ESC 键退出全屏
  if (event.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen()
  }
  // F11 键切换全屏（防止默认行为）
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
}
</script>

<style scoped>
.model-editor {
  @apply flex flex-col h-screen bg-gray-100;
}

.header {
  @apply bg-gray-800 text-white p-4 text-center;
}

.controls {
  @apply p-3 bg-gray-200 flex justify-center items-center gap-3 flex-wrap;
}

.btn-primary {
  @apply bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded transition-colors;
}

.btn-export {
  @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.model-info {
  @apply text-sm text-gray-700 ml-5;
}

.scene-container {
  @apply flex-1 relative overflow-hidden;
}

.animations-panel {
  @apply absolute top-3 right-3 bg-black bg-opacity-70 text-white p-4 rounded w-80 max-w-sm overflow-y-auto shadow-lg;
  max-height: 80vh;
}

.animations-list {
  @apply space-y-2;
}

.animation-item {
  @apply flex justify-between items-center p-3 bg-white bg-opacity-10 rounded transition-colors flex-wrap gap-2;
}

.animation-item.active {
  @apply bg-green-500 bg-opacity-30 border-l-4 border-green-500;
}

.animation-name {
  @apply flex-1 min-w-0 cursor-pointer p-1 rounded transition-colors hover:bg-white hover:bg-opacity-20;
  min-width: 150px;
}

.animation-name-edit {
  @apply flex-1 min-w-0 bg-white bg-opacity-10 border border-white border-opacity-30 rounded text-white p-1 text-sm;
  min-width: 150px;
}

.animation-controls {
  @apply flex gap-1 flex-wrap justify-end min-w-0;
  min-width: 120px;
}

.btn {
  @apply cursor-pointer px-2 py-1 rounded border-none text-xs transition-colors;
}

.btn-play {
  @apply bg-green-500 text-white hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-stop {
  @apply bg-red-500 text-white hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-delete {
  @apply bg-gray-500 text-white hover:bg-gray-600;
}

.no-animations {
  @apply italic text-center text-gray-300;
}

.loading {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-50;
}

.loading-content {
  @apply bg-black bg-opacity-70 text-white p-5 rounded flex flex-col items-center;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mb-3;
}

.loading-text {
  @apply text-center;
}

.instructions {
  @apply absolute bottom-3 left-3 bg-black bg-opacity-50 text-white p-3 rounded text-xs;
}
</style>
