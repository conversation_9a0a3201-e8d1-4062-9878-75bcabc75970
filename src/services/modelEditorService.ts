// 使用全局THREE对象（通过CDN加载）
declare global {
  interface Window {
    THREE: any
  }
}

// 动态加载Three.js脚本
let threeLoaded = false
let threeLoadPromise: Promise<void> | null = null

function loadThreeJS(): Promise<void> {
  if (threeLoaded) {
    return Promise.resolve()
  }

  if (threeLoadPromise) {
    return threeLoadPromise
  }

  threeLoadPromise = new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.THREE) {
      threeLoaded = true
      resolve()
      return
    }

    // 创建script标签加载Three.js
    const script = document.createElement('script')
    script.type = 'module'
    script.innerHTML = `
      import * as THREE from 'https://unpkg.com/three@0.160.0/build/three.module.js'
      import { OrbitControls } from 'https://unpkg.com/three@0.160.0/examples/jsm/controls/OrbitControls.js'
      import { GLTFLoader } from 'https://unpkg.com/three@0.160.0/examples/jsm/loaders/GLTFLoader.js'
      import { DRACOLoader } from 'https://unpkg.com/three@0.160.0/examples/jsm/loaders/DRACOLoader.js'
      import { GLTFExporter } from 'https://unpkg.com/three@0.160.0/examples/jsm/exporters/GLTFExporter.js'

      window.THREE = THREE
      window.THREE.OrbitControls = OrbitControls
      window.THREE.GLTFLoader = GLTFLoader
      window.THREE.DRACOLoader = DRACOLoader
      window.THREE.GLTFExporter = GLTFExporter

      window.dispatchEvent(new CustomEvent('threeLoaded'))
    `

    // 监听加载完成事件
    window.addEventListener('threeLoaded', () => {
      threeLoaded = true
      resolve()
    }, { once: true })

    script.onerror = () => {
      reject(new Error('Failed to load Three.js'))
    }

    document.head.appendChild(script)
  })

  return threeLoadPromise
}

// 获取THREE对象的辅助函数
function getTHREE() {
  if (!window.THREE) {
    throw new Error('Three.js not loaded')
  }
  return window.THREE
}

// 模型信息接口
export interface ModelInfo {
  name: string
  vertices: number
  size: number
}

// 动画信息接口
export interface AnimationInfo {
  name: string
  duration: number
  index: number
}

// 模型编辑器类
export class ModelEditorService {
  private scene: any = null
  private camera: any = null
  private renderer: any = null
  private controls: any = null
  private currentModel: any = null
  private originalGltf: any = null
  private currentFileName = ''
  private mixer: any = null
  private animations: any[] = []
  private activeAction: any = null
  private activeAnimationIndex = -1
  private clock: any = null
  private hasModelChanged = false
  private container: HTMLElement | null = null

  // 初始化场景
  async init(container: HTMLElement): Promise<void> {
    // 首先加载Three.js
    await loadThreeJS()

    const THREE = getTHREE()
    this.container = container

    // 创建场景
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0xf0f0f0)

    // 创建相机
    const aspect = container.clientWidth / container.clientHeight
    this.camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000)
    this.camera.position.set(0, 5, 10)

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true })
    this.renderer.setSize(container.clientWidth, container.clientHeight)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.outputColorSpace = THREE.SRGBColorSpace
    container.appendChild(this.renderer.domElement)

    // 添加轨道控制器
    this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05

    // 添加灯光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
    this.scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
    directionalLight.position.set(1, 1, 1)
    this.scene.add(directionalLight)

    // 添加坐标轴辅助
    const axesHelper = new THREE.AxesHelper(5)
    this.scene.add(axesHelper)

    // 添加网格地面
    const gridHelper = new THREE.GridHelper(10, 10)
    this.scene.add(gridHelper)

    // 创建时钟
    this.clock = new THREE.Clock()

    // 监听窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this))

    // 开始动画循环
    this.animate()
  }

  // 窗口大小变化处理
  private onWindowResize(): void {
    if (!this.camera || !this.renderer || !this.container) return

    this.camera.aspect = this.container.clientWidth / this.container.clientHeight
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
  }

  // 动画循环
  private animate(): void {
    requestAnimationFrame(() => this.animate())

    // 更新控制器
    if (this.controls) {
      this.controls.update()
    }

    // 更新动画混合器
    if (this.mixer) {
      const delta = this.clock.getDelta()
      this.mixer.update(delta)
    }

    // 渲染场景
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera)
    }
  }

  // 加载GLTF模型
  async loadGLTFModel(file: File): Promise<ModelInfo> {
    // 确保Three.js已经加载
    await loadThreeJS()
    const THREE = getTHREE()

    return new Promise((resolve, reject) => {
      // 保存当前文件名
      this.currentFileName = file.name

      // 重置模型状态
      this.hasModelChanged = false

      // 移除之前的模型
      if (this.currentModel && this.scene) {
        this.scene.remove(this.currentModel)
        this.currentModel = null
        this.originalGltf = null
      }

      // 创建文件URL
      const fileURL = URL.createObjectURL(file)

      // 设置GLTF加载器
      const loader = new THREE.GLTFLoader()

      // 可选：添加Draco解码器支持压缩模型
      const dracoLoader = new THREE.DRACOLoader()
      dracoLoader.setDecoderPath('https://unpkg.com/three@0.160.0/examples/jsm/libs/draco/')
      loader.setDRACOLoader(dracoLoader)

      // 加载模型
      loader.load(
        fileURL,
        async (gltf) => {
          try {
            // 加载成功
            this.originalGltf = gltf
            this.currentModel = gltf.scene

            // 计算模型边界框并居中
            const box = new THREE.Box3().setFromObject(this.currentModel)
            const size = box.getSize(new THREE.Vector3()).length()
            const center = new THREE.Vector3()

            // 调整相机位置
            if (this.camera && this.controls) {
              this.camera.position.copy(center)
              this.camera.position.x += size / 2.0
              this.camera.position.y += size / 5.0
              this.camera.position.z += size / 2.0
              this.camera.lookAt(center)

              // 调整控制器目标
              this.controls.target.copy(center)
            }

            // 添加模型到场景
            if (this.scene) {
              this.scene.add(this.currentModel)
            }

            // 处理动画
            this.handleAnimations(gltf)

            // 计算模型信息
            const vertices = this.countVertices(this.currentModel)
            const modelInfo: ModelInfo = {
              name: file.name,
              vertices,
              size: file.size
            }

            // 释放文件URL
            URL.revokeObjectURL(fileURL)

            resolve(modelInfo)
          } catch (error) {
            console.error('处理模型时出错:', error)
            URL.revokeObjectURL(fileURL)
            reject(error)
          }
        },
        (xhr) => {
          // 加载进度
          const percent = Math.floor((xhr.loaded / xhr.total) * 100)
          console.log(`加载进度: ${percent}%`)
        },
        (error) => {
          // 加载错误
          console.error('加载模型时出错:', error)
          URL.revokeObjectURL(fileURL)
          reject(error)
        }
      )
    })
  }

  // 计算模型顶点数
  private countVertices(object: THREE.Object3D): number {
    let vertexCount = 0
    object.traverse((child) => {
      if ((child as THREE.Mesh).isMesh) {
        const mesh = child as THREE.Mesh
        const geometry = mesh.geometry
        if (geometry.attributes.position) {
          vertexCount += geometry.attributes.position.count
        }
      }
    })
    return vertexCount
  }

  // 处理模型动画
  private handleAnimations(gltf: any): void {
    const THREE = getTHREE()

    // 重置之前的动画状态
    if (this.mixer) {
      this.mixer.stopAllAction()
      this.mixer.uncacheRoot(this.mixer.getRoot())
      this.mixer = null
    }

    // 清空动画列表
    this.animations = []
    this.activeAction = null
    this.activeAnimationIndex = -1

    // 检查模型是否有动画
    if (gltf.animations && gltf.animations.length > 0 && this.currentModel) {
      // 创建动画混合器
      this.mixer = new THREE.AnimationMixer(this.currentModel)

      // 复制动画数组
      this.animations = [...gltf.animations]
    }
  }

  // 获取动画列表
  getAnimations(): AnimationInfo[] {
    return this.animations.map((animation, index) => ({
      name: animation.name || `动作 ${index + 1}`,
      duration: animation.duration,
      index
    }))
  }

  // 播放指定索引的动画
  playAnimation(index: number): boolean {
    if (!this.mixer || !this.animations || index < 0 || index >= this.animations.length) {
      console.warn('无法播放动画: 无效的索引或状态')
      return false
    }

    const animation = this.animations[index]
    if (!animation) {
      console.warn(`无法播放动画: 索引 ${index} 处的动画无效`)
      return false
    }

    // 停止当前正在播放的动画
    if (this.activeAction) {
      this.activeAction.stop()
      this.activeAction = null
    }

    try {
      // 创建新的动作并播放
      this.activeAction = this.mixer.clipAction(animation)
      this.activeAction.reset()
      this.activeAction.play()

      // 更新当前活动的动画索引
      this.activeAnimationIndex = index

      console.log(`正在播放动画: ${animation.name || `动作 ${index + 1}`}`)
      return true
    } catch (e) {
      console.error('播放动画时出错:', e)
      this.activeAction = null
      this.activeAnimationIndex = -1
      return false
    }
  }

  // 停止当前正在播放的动画
  stopAnimation(): void {
    if (!this.mixer || !this.activeAction) return

    const index = this.activeAnimationIndex

    try {
      // 停止动画
      this.activeAction.stop()

      // 记录日志
      let animationName = '未知动画'
      if (index >= 0 && index < this.animations.length && this.animations[index]) {
        animationName = this.animations[index].name || `动作 ${index + 1}`
      }
      console.log(`停止动画: ${animationName}`)
    } catch (e) {
      console.warn('停止动画时出错:', e)
    } finally {
      // 无论是否出错，都重置状态
      this.activeAction = null
      this.activeAnimationIndex = -1
    }
  }

  // 删除指定索引的动画
  deleteAnimation(index: number): boolean {
    if (index >= this.animations.length || index < 0) return false

    // 保存要删除的动画信息
    const animationToRemove = this.animations[index]
    const animationName = animationToRemove.name || `动作 ${index + 1}`

    // 处理正在播放的动画
    if (this.mixer) {
      // 如果正在播放该动画，先停止
      if (index === this.activeAnimationIndex) {
        if (this.activeAction) {
          this.activeAction.stop()
          this.activeAction = null
        }
        this.activeAnimationIndex = -1
      }
      // 如果删除的是当前活动动画之前的动画，需要调整索引
      else if (index < this.activeAnimationIndex) {
        this.activeAnimationIndex--
      }

      // 从混合器中移除该动画的动作
      try {
        const action = this.mixer.clipAction(animationToRemove)
        action.stop()
        this.mixer.uncacheAction(animationToRemove)
      } catch (e) {
        console.warn('清除动画动作时出错:', e)
      }
    }

    // 从列表中移除该动画
    this.animations.splice(index, 1)
    console.log(`删除动画: ${animationName}`)

    // 从原始 GLTF 对象中也删除该动画
    if (this.originalGltf && this.originalGltf.animations) {
      this.originalGltf.animations.splice(index, 1)

      // 标记模型已被编辑
      this.hasModelChanged = true
    }

    return true
  }

  // 重命名动画
  renameAnimation(index: number, newName: string): boolean {
    if (index < 0 || index >= this.animations.length || !newName.trim()) {
      return false
    }

    const oldName = this.animations[index].name || `动作 ${index + 1}`

    // 更新动画名称
    this.animations[index].name = newName.trim()

    // 更新原始 GLTF 对象中的动画名称
    if (this.originalGltf && this.originalGltf.animations && this.originalGltf.animations[index]) {
      this.originalGltf.animations[index].name = newName.trim()

      // 标记模型已被编辑
      this.hasModelChanged = true
    }

    console.log(`已将动画名称从 "${oldName}" 更改为 "${newName}"`)
    return true
  }

  // 获取当前活动的动画索引
  getActiveAnimationIndex(): number {
    return this.activeAnimationIndex
  }

  // 检查模型是否被编辑过
  isModelChanged(): boolean {
    return this.hasModelChanged
  }

  // 导出模型为GLB文件
  async exportToGLB(): Promise<ArrayBuffer> {
    await loadThreeJS()
    const THREE = getTHREE()

    return new Promise((resolve, reject) => {
      if (!this.originalGltf || !this.currentModel) {
        reject(new Error('没有可导出的模型'))
        return
      }

      // 创建导出器
      const exporter = new THREE.GLTFExporter()

      // 准备导出选项
      const options = {
        binary: true, // 导出为二进制 GLB 格式
        animations: this.originalGltf.animations, // 使用编辑后的动画列表
        onlyVisible: false // 导出所有对象，包括不可见的
      }

      // 开始导出
      exporter.parse(
        this.currentModel.children,
        (result) => {
          // 导出成功
          resolve(result as ArrayBuffer)
        },
        (error) => {
          // 导出失败
          console.error('导出模型时出错:', error)
          reject(error)
        },
        options
      )
    })
  }

  // 生成导出文件名
  getExportFileName(): string {
    const baseName = this.currentFileName.replace(/\.[^\.]+$/, '') // 移除扩展名
    return `${baseName}_edited.glb`
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB'
    else return (bytes / 1048576).toFixed(2) + ' MB'
  }

  // 销毁资源
  dispose(): void {
    // 停止动画
    this.stopAnimation()

    // 清理混合器
    if (this.mixer) {
      this.mixer.stopAllAction()
      this.mixer.uncacheRoot(this.mixer.getRoot())
      this.mixer = null
    }

    // 清理场景
    if (this.scene && this.currentModel) {
      this.scene.remove(this.currentModel)
    }

    // 清理渲染器
    if (this.renderer && this.container) {
      this.container.removeChild(this.renderer.domElement)
      this.renderer.dispose()
    }

    // 移除事件监听器
    window.removeEventListener('resize', this.onWindowResize.bind(this))

    // 重置状态
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.currentModel = null
    this.originalGltf = null
    this.animations = []
    this.activeAction = null
    this.activeAnimationIndex = -1
    this.hasModelChanged = false
  }
}
